import tkinter as tk
from tkinter import messagebox, scrolledtext
import pyperclip
import threading
import time

class CopyPasteApp:
    def __init__(self, root):
        self.root = root
        self.root.title("複製貼上工具")
        self.root.geometry("500x400")
        self.root.resizable(True, True)
        
        # 設置字體
        self.font = ("Arial", 12)
        
        # 創建主框架
        main_frame = tk.Frame(root, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 標題
        title_label = tk.Label(main_frame, text="複製貼上工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 按鈕框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=(0, 10))
        
        # 複製按鈕 (Ctrl+C)
        self.copy_button = tk.Button(
            button_frame, 
            text="複製 (Ctrl+C)", 
            command=self.copy_text,
            font=self.font,
            bg="#4CAF50",
            fg="white",
            width=15,
            height=2
        )
        self.copy_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 貼上按鈕 (Ctrl+V)
        self.paste_button = tk.Button(
            button_frame, 
            text="貼上 (Ctrl+V)", 
            command=self.paste_text,
            font=self.font,
            bg="#2196F3",
            fg="white",
            width=15,
            height=2
        )
        self.paste_button.pack(side=tk.LEFT)
        
        # 文字區域標籤
        text_label = tk.Label(main_frame, text="文字編輯區域:", font=self.font)
        text_label.pack(anchor=tk.W, pady=(10, 5))
        
        # 文字編輯區域
        self.text_area = scrolledtext.ScrolledText(
            main_frame, 
            wrap=tk.WORD, 
            font=self.font,
            height=15,
            width=60
        )
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 狀態標籤
        self.status_label = tk.Label(
            main_frame, 
            text="準備就緒", 
            font=("Arial", 10),
            fg="green"
        )
        self.status_label.pack(anchor=tk.W)
        
        # 剪貼板內容顯示區域
        clipboard_label = tk.Label(main_frame, text="目前剪貼板內容:", font=("Arial", 10))
        clipboard_label.pack(anchor=tk.W, pady=(10, 5))
        
        self.clipboard_display = tk.Text(
            main_frame, 
            height=3, 
            font=("Arial", 9),
            bg="#f0f0f0",
            state=tk.DISABLED
        )
        self.clipboard_display.pack(fill=tk.X, pady=(0, 10))
        
        # 綁定鍵盤快捷鍵
        self.root.bind('<Control-c>', lambda e: self.copy_text())
        self.root.bind('<Control-v>', lambda e: self.paste_text())
        
        # 啟動剪貼板監控
        self.start_clipboard_monitor()
        
    def copy_text(self):
        """複製選中的文字到剪貼板"""
        try:
            # 獲取選中的文字
            selected_text = self.text_area.selection_get()
            if selected_text:
                pyperclip.copy(selected_text)
                self.update_status(f"已複製: {selected_text[:30]}{'...' if len(selected_text) > 30 else ''}", "green")
            else:
                # 如果沒有選中文字，複製全部內容
                all_text = self.text_area.get("1.0", tk.END).strip()
                if all_text:
                    pyperclip.copy(all_text)
                    self.update_status(f"已複製全部內容: {all_text[:30]}{'...' if len(all_text) > 30 else ''}", "green")
                else:
                    self.update_status("沒有文字可複製", "orange")
        except tk.TclError:
            # 沒有選中文字的情況
            all_text = self.text_area.get("1.0", tk.END).strip()
            if all_text:
                pyperclip.copy(all_text)
                self.update_status(f"已複製全部內容: {all_text[:30]}{'...' if len(all_text) > 30 else ''}", "green")
            else:
                self.update_status("沒有文字可複製", "orange")
        except Exception as e:
            self.update_status(f"複製失敗: {str(e)}", "red")
    
    def paste_text(self):
        """從剪貼板貼上文字"""
        try:
            clipboard_content = pyperclip.paste()
            if clipboard_content:
                # 在游標位置插入文字
                cursor_pos = self.text_area.index(tk.INSERT)
                self.text_area.insert(cursor_pos, clipboard_content)
                self.update_status(f"已貼上: {clipboard_content[:30]}{'...' if len(clipboard_content) > 30 else ''}", "blue")
            else:
                self.update_status("剪貼板是空的", "orange")
        except Exception as e:
            self.update_status(f"貼上失敗: {str(e)}", "red")
    
    def update_status(self, message, color="black"):
        """更新狀態標籤"""
        self.status_label.config(text=message, fg=color)
        # 3秒後恢復預設狀態
        self.root.after(3000, lambda: self.status_label.config(text="準備就緒", fg="green"))
    
    def update_clipboard_display(self):
        """更新剪貼板內容顯示"""
        try:
            clipboard_content = pyperclip.paste()
            self.clipboard_display.config(state=tk.NORMAL)
            self.clipboard_display.delete("1.0", tk.END)
            if clipboard_content:
                # 限制顯示長度
                display_content = clipboard_content[:200] + ("..." if len(clipboard_content) > 200 else "")
                self.clipboard_display.insert("1.0", display_content)
            else:
                self.clipboard_display.insert("1.0", "(剪貼板是空的)")
            self.clipboard_display.config(state=tk.DISABLED)
        except Exception:
            self.clipboard_display.config(state=tk.NORMAL)
            self.clipboard_display.delete("1.0", tk.END)
            self.clipboard_display.insert("1.0", "(無法讀取剪貼板)")
            self.clipboard_display.config(state=tk.DISABLED)
    
    def start_clipboard_monitor(self):
        """啟動剪貼板監控線程"""
        def monitor():
            last_clipboard = ""
            while True:
                try:
                    current_clipboard = pyperclip.paste()
                    if current_clipboard != last_clipboard:
                        last_clipboard = current_clipboard
                        # 在主線程中更新UI
                        self.root.after(0, self.update_clipboard_display)
                except Exception:
                    pass
                time.sleep(1)  # 每秒檢查一次
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()

def main():
    # 檢查是否安裝了pyperclip
    try:
        import pyperclip
    except ImportError:
        print("錯誤: 需要安裝 pyperclip 模組")
        print("請執行: pip install pyperclip")
        return
    
    root = tk.Tk()
    app = CopyPasteApp(root)
    
    # 設置窗口圖標（如果有的話）
    try:
        root.iconbitmap("icon.ico")  # 可選：如果有圖標文件
    except:
        pass
    
    root.mainloop()

if __name__ == "__main__":
    main()
